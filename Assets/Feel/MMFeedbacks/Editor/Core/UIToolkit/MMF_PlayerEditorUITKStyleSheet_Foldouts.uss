.mm-foldout { /* generic foldout */
    margin-top: 5px;
    border-left-color: black;
    border-left-width: 3px;
    margin-bottom: 0px;
    padding-right: 0px;
    padding-left: 0;
    padding-bottom: 0;
    padding-top: 0;
}

.mm-foldout-toggle { /* generic foldout toggle */
    padding-top: 5px;
    padding-left: 5px;
    margin-left: 0;
    padding-bottom: 5px;
    margin-bottom: 0;
    -unity-font-style: bold;
}

.mm-foldout #unity-content { /* generic foldout content */
    padding-top: 2px;
    margin-top: 0;
    margin-left: 10px;
    padding-left: 10px;
    padding-right: 8px;
    padding-bottom: 10px;
}

.mm-foldout .unity-header-drawer__label { /* [Header] attributes inside foldout content */
    margin-top: 10px;
    padding-bottom: 5px;
    padding-top: 5px;
    margin-left: -20px;
    padding-left: 20px;
    -unity-font-style: bold;
    background-color: rgba(255, 255, 255, 0.04);
}