.mm-foldout .unity-header-drawer__label { /* [Header] attributes inside foldout content */
    background-color: rgba(255, 255, 255, 0.00);
}

.mm-feedback-foldout-label,
.mm-feedback-foldout-label:hover,
.mm-feedback-foldout-label:focus,
.mm-feedback-foldout-label:active,
.mm-feedback-foldout-label:selected,
.mm-feedback-foldout-label:checked,
.mm-feedbacks-list Label:hover,
.mm-feedbacks-list Label:focus,
.mm-feedbacks-list Label:active { /* labels in the list */
    color: rgba(0, 0, 0, 0.9);
}

.mm-mmf-group .mm-foldout Label,
.mm-mmf-group .mm-foldout Label:hover,
.mm-mmf-group .mm-foldout Label:focus,
.mm-mmf-group .mm-foldout Label:active,
.mm-mmf-group .mm-foldout Label:selected,
.mm-mmf-group .mm-foldout Label:checked { /* labels in the foldout groups */
    color: rgba(0, 0, 0, 1);
}

.mm-feedbacks-list-title, .mm-feedbacks-list-title:hover { /* feedbacks list title */
    color: rgba(0, 0, 0, 1);
}

.mm-feedbacks-list #unity-list-view__reorderable-item { /* feedbacks list item, controls the bg color of the feedback header */
    background-color: rgba(0, 0, 0, 0);
}

.mm-feedbacks-list #unity-list-view__reorderable-handle #unity-list-view__reorderable-handle-bar { /* repositionable handle icon horizontal bars */
    background-color: rgba(0, 0, 0, .3);
}

.mm-feedback-help-box { /* help box */
    background-color: rgba(255, 255, 255, 0.1);
}

.mm-feedback-help-box Label, .mm-feedback-help-box Label:hover { /* help box labels */
    color: rgba(100, 100, 100, 1);
}

.mm-feedback-setup-required-box { /* setup required box */
    color: rgba(20, 20, 20, 1);
}

.mm-feedback-setup-required-box Label, .mm-feedback-setup-required-box Label:hover { /* setup required box labels */
    color: rgba(20, 20, 20, 1);
    background-color: rgb(255, 97, 33);
}

.mm-mmf-inspector { /* per feedback inspector */
    background-color: rgba(255, 255, 255, 0.1);
}

.mm-mmf-inspector .unity-decorator-drawers-container .unity-header-drawer__label,
.mm-mmf-inspector .unity-decorator-drawers-container .unity-header-drawer__label:hover{ /* Header attributes */
    border-color: rgba(59,163,196,0.3);
    color: rgba(12,13,18,1);
}

.mm-mmf-group .mm-foldout { /* feedback foldout */
    background-color: rgba(0, 0, 0, 0.00);
}

.mm-mmf-group .mm-foldout-toggle { /* feedback foldout toggle */
    background-color: rgba(0, 0, 0, 0.08);
}

.mm-feedback-control-buttons { /* play and stop buttons at the bottom */
    background-color: rgba(255, 255, 255, 0.1);
}
.mm-feedback-required-target-label { /* required target label */
    color: rgba(255,255,255,0.4);
}

.mm-feedback-timing-label { /* required timing label */
    color: rgba(255,255,255,0.4);
}

.mm-feedbacks-list-empty Label { /* custom empty list label */
    background-color: rgba(0, 0, 0, 0.2);
}

.mm-bottom-bar { /* bottom bar */
    border-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.1);
}

.unity-list-view__reorderable-item {
    color: rgba(0, 0, 0, 1);
}

.mm-settings-foldout { /* settings foldout */
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(255, 255, 255, 0);
}

.mm-settings-foldout Label, mm-settings-foldout Label:hover, mm-settings-foldout Label:active { /* settings foldout label */
    color: rgba(0, 0, 0, 1);
}

Toggle.mm-settings-foldout-toggle { /* settings foldout toggle */
    background-color: rgba(0, 0, 0, 0.1);
}

.mm-settings-foldout-sub { /* settings sub foldout */
    background-color: rgba(0, 0, 0, 0.1);
}

Toggle.mm-settings-foldout-sub-toggle { /* settings sub foldout toggle */
    background-color: rgba(0, 0, 0, 0.1);
}

.mm-settings-is-playing {
    background-color: #FFC400;
}