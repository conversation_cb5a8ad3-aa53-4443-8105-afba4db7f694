.mm-settings-foldout { /* settings foldout */
    background-color: rgba(0, 0, 0, 0);
    margin-bottom: 5px;
    border-left-width: 0px;
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 1px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
    padding-right: 0px;
}

.mm-settings-foldout Label, mm-settings-foldout Label:hover, mm-settings-foldout Label:active { /* settings foldout label */
    color: rgba(220, 220, 220, 1);
}

.mm-settings-foldout .unity-toggle__input {
    padding: 3px;
}

.mm-settings-icon { /* settings icon */
    margin-top: 2px;
}

Toggle.mm-settings-foldout-toggle { /* settings foldout toggle */
    background-color: rgba(0, 0, 0, 0.3);
    padding-left: 10px;
    -unity-background-scale-mode: scale-to-fit;
}

.mm-settings-foldout-sub { /* settings sub foldout */
    background-color: rgba(0, 0, 0, 0.1);
    margin-left: -5px;
}

Toggle.mm-settings-foldout-sub-toggle { /* settings sub foldout toggle */
    background-color: rgba(0, 0, 0, 0.4);
    margin-top: 0px;
}

.mm-settings-info {
    flex-direction: row;
    -unity-font-style: normal;
}

.mm-settings-is-playing {
    background-color: #FFC400;
    color: black;
    padding: 2px;
    padding-left: 5px;
    padding-right: 5px;
    -unity-font-style: bold;
    right: 75px;
    position: absolute;
    display: none;
}