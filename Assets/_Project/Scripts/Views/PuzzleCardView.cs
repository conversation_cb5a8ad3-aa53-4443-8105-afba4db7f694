using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.InputSystem;

public struct PuzzleCardClickedEvent : IEvent
{
    public SessionID CardID;

    public PuzzleCardClickedEvent(SessionID cardID)
    {
        CardID = cardID;
    }
}

public struct PuzzleCardLandedOnPileEvent : IEvent
{
    public SessionID CardID;

    public PuzzleCardLandedOnPileEvent(SessionID cardID)
    {
        CardID = cardID;
    }
}

public class PuzzleCardView : BaseCardView
{
    [SerializeField] private int visualCardIndex;
    
    public SessionID CardID { get; private set; }
    
    public int VisualCardIndex => visualCardIndex;

    private UniTask _refreshOperation;

    protected override void Start()
    {
        base.Start();
        
        //  TODO: Should be called when instantiated by a third party
        RefreshCardVisualStatus().Forget();

        _subscriptions.Add(Game.EventHub.Subscribe<PuzzleCardPlayedEvent>(OnCardPlayed));
        _subscriptions.Add(Game.EventHub.Subscribe<InputTapEvent>(CheckCardInteraction));
    }

    public void Init(CardData cardData)
    {
        visualCardIndex = cardData.VisualCardIndex;
        CardID = cardData.ID;

        RefreshCardVisualStatus().Forget();
    }

    private void OnCardPlayed(PuzzleCardPlayedEvent evt)
    {
        if (evt.CardID == CardID)
        {
            HandleCardPlayed();
        }
        else
        {
            // TODO: Flipping a card should be done via event directly to the card
            RefreshCardVisualStatus(true).Forget();
        }
    }

    public void HandleCardPlayed()
    {
        //  TODO: Extract settings back into editor
        _subscriptions.Dispose();
        
        HandleCardFlightAnimation(Game.DataManager.Session.SceneObjects.CardsFlightTargetPosition);
    }

    public async UniTask RefreshCardVisualStatus(bool animated = false)
    {
        _refreshOperation = RefreshCardVisualStatusInternal(animated);
        await _refreshOperation;
    }

    private async UniTask RefreshCardVisualStatusInternal(bool animated)
    {
        var card = Game.DataManager.Session.CurrentPuzzle.GetCardByVisualPosition(visualCardIndex);
        
        if (card == null)
        {
            return;
        }
        
        CardID = card.ID;

        bool isAvailable = card.BlockedByCards.Count == 0;

        if (animated && hiddenViewContainer.activeSelf && isAvailable)
        {
            FlipCard(true);
        }
        else
        {
            hiddenViewContainer.SetActive(!isAvailable);
            revealedViewContainer.SetActive(isAvailable);
        }
        
        await LoadCardsSprite(card);
    }

    private async UniTask LoadCardsSprite(CardData card)
    {
        string spriteAssetName = GameUtils.GetCardSpriteNameByCard(card);
        await LoadCardSprite(spriteAssetName);
    }

    private void CheckCardInteraction(InputTapEvent evt)
    {
        Ray ray = Game.GameplayCamera.ScreenPointToRay(new Vector3(evt.Position.x, evt.Position.y, 0));
        
        if (Physics.Raycast(ray, out RaycastHit hit, Consts.RAYCAST_DISTANCE_TO_CARDS))
        {
            if (hit.collider && hit.collider.gameObject == gameObject)
            {
                HandleCardClicked();
            }
        }
    }

    private void HandleCardClicked()
    {
        if(hiddenViewContainer.activeSelf) return;
        
        Game.EventHub.Notify(new PuzzleCardClickedEvent(CardID));
    }

    public void OnAnimationPuzzleCardLanded()
    {
        Game.EventHub.Notify(new PuzzleCardLandedOnPileEvent(CardID));
    }

    public void MarkForDestruction()
    {
        if(_refreshOperation.Status == UniTaskStatus.Pending)
        {
            _refreshOperation.ContinueWith(() => Destroy(gameObject));
        }
        else
        {
            Destroy(gameObject);
        }
    }
}