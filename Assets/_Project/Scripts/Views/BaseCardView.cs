using Cysharp.Threading.Tasks;
using MoreMountains.Feedbacks;
using MoreMountains.Tools;
using UnityEngine;

public class BaseCardView : BaseView
{
    [SerializeField] protected Transform animationContainer;
    [SerializeField] protected GameObject hiddenViewContainer;
    [Serial<PERSON>Field] protected GameObject revealedViewContainer;
    [SerializeField] protected SpriteR<PERSON>er revealedCardSprite;
    
    [SerializeField] protected MMF_Player cardFlipFeedbacks;
    [SerializeField] protected MMF_Player cardFlightFeedbacks;

    protected virtual void Start()
    {
        // Base implementation
    }

    public virtual void FlipCard(bool isAnimated = true)
    {
        if (isAnimated)
        {
            FlipCardToRevealed();
        }
        else
        {
            hiddenViewContainer.SetActive(false);
            revealedViewContainer.SetActive(true);
        }
    }

    public virtual void HandleCardFlightAnimation(Vector3 destinationPosition)
    {
        MMF_Position positionFeedback = cardFlightFeedbacks.GetFeedbackOfType<MMF_Position>();
        
        // Essential position settings
        positionFeedback.Mode = MMF_Position.Modes.ToDestination;
        positionFeedback.Space = MMF_Position.Spaces.World;
        positionFeedback.RelativePosition = false;
        
        // Clear any transform references to ensure we use Vector3 positions
        positionFeedback.InitialPositionTransform = null;
        positionFeedback.DestinationPositionTransform = null;
        
        // Set positions directly
        positionFeedback.InitialPosition = transform.position;
        positionFeedback.DestinationPosition = destinationPosition;
        
        positionFeedback.DeterminePositionsOnPlay = true;
        
        // Animation settings
        positionFeedback.AnimatePositionTween = new MMTweenType(new AnimationCurve(
            new Keyframe(0, 0),
            new Keyframe(0.5f, 1.05f),
            new Keyframe(1, 1)
        ));
        
        positionFeedback.FeedbackDuration = 1f;
        
        cardFlightFeedbacks.PlayFeedbacks();
    }

    protected virtual void FlipCardToRevealed()
    {
        // Start the flip animation
        cardFlipFeedbacks.PlayFeedbacks();
    }

    public virtual void OnAnimationFlipMidpoint()
    {
        hiddenViewContainer.SetActive(false);
        revealedViewContainer.SetActive(true);
    }

    protected virtual async UniTask LoadCardSprite(string spriteAssetName)
    {
        await Game.AssetManager.GetSpriteByName(spriteAssetName).ContinueWith(sprite =>
        {
            revealedCardSprite.sprite = sprite;
        });
    }
}
